import { insertJob } from "@/database/drizzle/queries/jobs";
import { redisClient } from "@/lib/redis";
import { Processor, Queue, Worker } from "bullmq";

const options = redisClient && {
  connection: redisClient,
};

export function w<TData>(queue: Queue<TData> | undefined, processor: Processor<TData>) {
  return () => {
    if (!queue) return;
    return new Worker<TData>(queue.name, processor, options);
  };
}

export async function addJob<TData>(db: DB, userId: string, queue: Queue<TData> | undefined, data: TData, delay = 0) {
  if (!queue) return;
  const job = (
    await insertJob(db, {
      name: queue.name,
      data: data,
      status: "pending",
      addedAt: new Date(),
      userId: userId,
    }).returning()
  )[0];
  return await queue.add(queue.name as any, data as any, { jobId: `${job.id}`, delay });
}

export const QGenerateVideo = options && new Queue<{ videoId: string }>("GenerateVideo", options);
export const QExtractThumbnail = options && new Queue<{ videoId: string }>("ExtractThumbnail", options);
