import { dbInit } from "@/database/drizzle/db";
import { uploadFile } from "@/database/drizzle/queries/files";
import { updateJob } from "@/database/drizzle/queries/jobs";
import * as drizzleQueries from "@/database/drizzle/queries/videos";
import { QExtractThumbnail, w } from "@/jobs/queues";
import axios from "axios";
import ffmpeg from "ffmpeg";
import fs from "fs";
import path from "path";
import { promisify } from "util";

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);

export const WExtractThumbnail = w(QExtractThumbnail, async (job) => {
  try {
    const db = dbInit();
    const { data } = job;

    await job.updateProgress({ value: 1, message: "Fetching video record" });

    // Get the video record
    const [record] = await drizzleQueries.getVideo(db, data.videoId);
    if (!record) {
      throw new Error(`Video with id ${data.videoId} not found`);
    }

    // Check if video has a videoUrl
    if (!record.videoUrl) {
      console.log(`Video ${data.videoId} does not have a videoUrl, stopping thumbnail extraction`);
      await updateJob(db, job.id!, { status: "completed", endedAt: new Date() });
      return { message: "No videoUrl found, skipping thumbnail extraction" };
    }

    await job.updateProgress({ value: 2, message: "Downloading video" });

    // Download the video file
    const videoResponse = await axios.get(record.videoUrl, {
      responseType: "arraybuffer",
    });
    
    if (!videoResponse.data) {
      throw new Error("Failed to download video");
    }

    const videoBuffer = Buffer.from(videoResponse.data);
    
    // Create temporary file paths
    const tempDir = "/tmp";
    const videoFileName = `video-${record.id}-${Date.now()}.mp4`;
    const thumbnailFileName = `thumbnail-${record.id}-${Date.now()}.jpg`;
    const videoPath = path.join(tempDir, videoFileName);
    const thumbnailPath = path.join(tempDir, thumbnailFileName);

    try {
      await job.updateProgress({ value: 3, message: "Saving video to temporary file" });

      // Write video to temporary file
      await writeFile(videoPath, videoBuffer);

      await job.updateProgress({ value: 4, message: "Extracting thumbnail with ffmpeg" });

      // Extract thumbnail using ffmpeg
      const video = await new ffmpeg(videoPath);
      
      // Extract thumbnail at 1 second mark (or at 10% of video duration)
      await video.fnExtractFrameToJPG(tempDir, {
        frame_rate: 1,
        number: 1,
        file_name: thumbnailFileName.replace('.jpg', ''),
      });

      await job.updateProgress({ value: 5, message: "Reading thumbnail file" });

      // Read the generated thumbnail
      const thumbnailBuffer = fs.readFileSync(thumbnailPath);

      await job.updateProgress({ value: 6, message: "Uploading thumbnail" });

      // Upload thumbnail using the same filename as video but with .thumbnail extension
      const videoBaseName = path.basename(record.videoUrl, path.extname(record.videoUrl));
      const thumbnailUploadName = `${videoBaseName}.thumbnail.jpg`;
      
      const thumbnailFile = await uploadFile(
        db,
        record.userId!,
        thumbnailBuffer,
        thumbnailUploadName
      );

      await job.updateProgress({ value: 7, message: "Cleaning up temporary files" });

      // Clean up temporary files
      await Promise.allSettled([
        unlink(videoPath),
        unlink(thumbnailPath),
      ]);

      await job.updateProgress({ value: 8, message: "Complete" });
      await updateJob(db, job.id!, { status: "completed", endedAt: new Date() });

      return {
        message: "Thumbnail extracted successfully",
        thumbnailUrl: thumbnailFile.url,
        thumbnailId: thumbnailFile.id,
      };

    } catch (ffmpegError) {
      // Clean up temporary files in case of error
      await Promise.allSettled([
        unlink(videoPath).catch(() => {}),
        unlink(thumbnailPath).catch(() => {}),
      ]);
      throw ffmpegError;
    }

  } catch (error) {
    console.error("Error in thumbnail extraction worker:", error);
    await updateJob(db, job.id!, { status: "completed", endedAt: new Date() });
    throw error;
  }
});
