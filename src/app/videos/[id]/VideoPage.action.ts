"use server";

import * as drizzleQueries from "@/database/drizzle/queries/videos";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

export async function onUpdateVideoVisibility(videoId: string, visibility: "public" | "private") {
  const context = await getDataContext();

  if (!context.session?.user?.id) {
    return { error: errors.NotAuthenticated };
  }

  // First, get the video to verify ownership
  const [video] = await drizzleQueries.getVideo(context.db, videoId);

  if (!video) {
    return { error: "Video not found" };
  }

  if (video.userId !== context.session.user.id) {
    return { error: "You don't have permission to modify this video" };
  }

  // Update the video visibility
  const updatedVideo = await drizzleQueries.updateVideo(context.db, videoId, {
    visibility,
  });

  return { success: true, video: updatedVideo };
}
