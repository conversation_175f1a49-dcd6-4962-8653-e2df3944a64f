import type { Metadata } from "next";
import { getData } from "./data";
import { DataContainer } from "./data.client";
import { VideoPage } from "./VideoPage";

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const data = await getData({ params });

  const title = data.video.target;
  const description = data.video.theme;
  const videoUrl = data.video.videoUrl;

  return {
    title,
    description,
    openGraph: videoUrl
      ? {
          title,
          description,
          type: "video.other",
          videos: [
            {
              url: videoUrl,
              type: "video/mp4",
            },
          ],
        }
      : undefined,
    twitter: videoUrl
      ? {
          card: "player",
          title,
          description,
          players: [
            {
              playerUrl: `${process.env.BASE_URL}/videos/${(await params).id}`,
              streamUrl: videoUrl,
              width: 720,
              height: 1280,
            },
          ],
        }
      : undefined,
  };
}

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const data = await getData({ params });

  return (
    <DataContainer data={data}>
      <VideoPage />
    </DataContainer>
  );
}
